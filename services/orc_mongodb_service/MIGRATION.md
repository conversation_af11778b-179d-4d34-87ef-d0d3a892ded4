# ORC MongoDB服务迁移指南

## 从版本 2.0.0 迁移到 3.0.0

### 概述

版本 3.0.0 是一个重大重构版本，主要改进包括：

1. **统一服务入口** - 集成所有子服务到单一主服务
2. **简化配置结构** - 合并分散的配置文件
3. **自动服务管理** - 自动启动和管理子服务
4. **集成监控服务** - 内置监控功能

### 主要变更

#### 1. 启动方式变更

**旧版本 (2.0.0)**:
```bash
# 启动所有微服务
python3 services/orc_mongodb_service/start_services.py

# 单独启动监控服务
python3 services/orc_mongodb_service/monitoring_service/monitor.py

# 单独处理ORC数据
python3 services/orc_mongodb_service/orc_processor_service/main.py --start-date 20250629
```

**新版本 (3.0.0)**:
```bash
# 统一启动入口 - 处理ORC数据（自动启动所有必要服务）
python3 services/orc_mongodb_service/run.py --start-date 20250629

# 运行监控模式
python3 services/orc_mongodb_service/run.py --monitor

# 检查服务状态
python3 services/orc_mongodb_service/run.py --status
```

#### 2. 配置文件结构变更

**旧版本配置结构**:
```
configs/orc_mongodb_service/
├── development.yaml                    # 主配置文件
├── production.yaml                     # 主配置文件
├── orc_processor_service/
│   ├── development.yaml               # ORC处理服务配置
│   └── production.yaml
├── mongodb_writer_service/
│   ├── development.yaml               # MongoDB写入服务配置
│   └── production.yaml
└── monitoring_service/
    ├── development.yaml               # 监控服务配置
    └── production.yaml
```

**新版本配置结构**:
```
configs/orc_mongodb_service/
├── development.yaml                    # 统一配置文件
├── production.yaml                     # 统一配置文件（已重构）
└── orc_mongodb_service.yaml           # 生产环境统一配置
```

#### 3. 配置文件内容变更

**旧版本配置** (分散在多个文件):
```yaml
# configs/orc_mongodb_service/development.yaml
services:
  orc_processor_service:
    config_file: "configs/orc_mongodb_service/orc_processor_service/development.yaml"
  mongodb_writer_service:
    config_file: "configs/orc_mongodb_service/mongodb_writer_service/development.yaml"
```

**新版本配置** (统一文件):
```yaml
# configs/orc_mongodb_service/development.yaml
orc_processor:
  start_date: "20250717"
  batch_processing:
    batch_size: 100

mongodb_writer_service:
  port: 8002
  batch_processing:
    batch_size: 5

redis:
  queue_control:
    pause_threshold: 30
    resume_threshold: 10
```

### 迁移步骤

#### 步骤 1: 备份现有配置

```bash
# 备份现有配置文件
cp -r configs/orc_mongodb_service configs/orc_mongodb_service.backup.$(date +%Y%m%d)
```

#### 步骤 2: 更新配置文件

1. **合并配置内容**:
   - 将 `orc_processor_service/*.yaml` 的内容合并到主配置文件的 `orc_processor` 节
   - 将 `mongodb_writer_service/*.yaml` 的内容合并到主配置文件的 `mongodb_writer_service` 节
   - 将 `monitoring_service/*.yaml` 的内容合并到主配置文件的 `monitoring_service` 节

2. **更新配置路径**:
   - 移除 `services` 节中的 `config_file` 引用
   - 直接在主配置文件中定义各服务配置

#### 步骤 3: 更新启动脚本

**替换现有启动脚本**:
```bash
# 旧的启动方式
# python3 services/orc_mongodb_service/start_services.py

# 新的启动方式
python3 services/orc_mongodb_service/run.py
```

**更新定时任务** (如果使用 cron):
```bash
# 旧的 cron 任务
# 0 2 * * * cd /path/to/project && python3 services/orc_mongodb_service/start_services.py

# 新的 cron 任务
0 2 * * * cd /path/to/project && python3 services/orc_mongodb_service/run.py --start-date $(date -d yesterday +%Y%m%d) --end-date $(date -d yesterday +%Y%m%d)
```

#### 步骤 4: 测试迁移结果

```bash
# 运行测试脚本验证配置
python3 services/orc_mongodb_service/test_service.py

# 检查服务状态
python3 services/orc_mongodb_service/status.py

# 运行监控模式验证功能
python3 services/orc_mongodb_service/run.py --monitor
```

### 配置映射表

| 旧配置位置 | 新配置位置 | 说明 |
|-----------|-----------|------|
| `orc_processor_service/development.yaml` | `orc_processor` 节 | ORC处理配置 |
| `mongodb_writer_service/development.yaml` | `mongodb_writer_service` 节 | MongoDB写入配置 |
| `monitoring_service/development.yaml` | `monitoring_service` 节 | 监控服务配置 |
| 各服务的 `redis` 配置 | 全局 `redis` 配置 | Redis配置统一 |
| 各服务的 `mongodb` 配置 | 全局 `mongodb` 配置 | MongoDB配置统一 |
| 各服务的 `milvus` 配置 | 全局 `milvus` 配置 | Milvus配置统一 |

### 新功能

#### 1. 队列长度自动控制

新版本增加了自动队列长度控制功能：

```yaml
redis:
  queue_control:
    check_interval: 10        # 检查间隔（秒）
    pause_threshold: 150      # 暂停阈值
    resume_threshold: 20      # 恢复阈值
```

#### 2. 延迟控制

新增延迟控制功能，用于性能优化：

```yaml
delay_control:
  batch_delay: 100           # 批次间延迟（毫秒）
  file_delay:
    delay_multiplier: 0.5    # 文件处理后延迟倍数
```

#### 3. 集成监控

监控功能现在集成在主服务中：

```bash
# 实时监控模式
python3 services/orc_mongodb_service/run.py --monitor

# 单次状态检查
python3 services/orc_mongodb_service/run.py --status
```

### 故障排除

#### 常见问题

1. **配置文件找不到**:
   ```
   错误: FileNotFoundError: configs/orc_mongodb_service/orc_processor_service/development.yaml
   解决: 确保已将子服务配置合并到主配置文件中
   ```

2. **服务启动失败**:
   ```
   错误: 服务初始化失败
   解决: 检查配置文件格式，确保所有必要配置节都存在
   ```

3. **端口冲突**:
   ```
   错误: 端口 8002 已被占用
   解决: 检查是否有旧版本服务仍在运行，或修改端口配置
   ```

#### 验证迁移成功

运行以下命令验证迁移是否成功：

```bash
# 1. 测试配置文件
python3 services/orc_mongodb_service/test_service.py

# 2. 检查服务状态
python3 services/orc_mongodb_service/status.py

# 3. 运行一个小测试
python3 services/orc_mongodb_service/run.py --province-ids 100 --start-date $(date +%Y%m%d) --end-date $(date +%Y%m%d)
```

### 回滚计划

如果迁移出现问题，可以按以下步骤回滚：

```bash
# 1. 停止新版本服务
pkill -f "services/orc_mongodb_service"

# 2. 恢复备份配置
rm -rf configs/orc_mongodb_service
mv configs/orc_mongodb_service.backup.$(date +%Y%m%d) configs/orc_mongodb_service

# 3. 使用旧版本启动方式
python3 services/orc_mongodb_service/start_services.py
```

### 支持

如果在迁移过程中遇到问题，请：

1. 检查日志文件: `logs/orc_mongodb_service/orc_mongodb_service.log`
2. 运行测试脚本: `python3 services/orc_mongodb_service/test_service.py`
3. 查看服务状态: `python3 services/orc_mongodb_service/status.py`
