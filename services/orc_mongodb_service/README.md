# ORC MongoDB服务 - 重构版本 3.0.0

## 概述

重构后的ORC MongoDB服务提供了统一的服务入口，集成了监控服务和自动启动功能，简化了配置文件结构。

## 主要改进

### 1. 统一服务入口
- **主服务**: `services/orc_mongodb_service/main.py` - 统一的服务管理器
- **简化启动**: `services/orc_mongodb_service/run.py` - 简化的启动脚本
- **集成监控**: 自动集成监控服务，无需单独启动

### 2. 简化配置结构
- **统一配置**: 所有子服务配置集成到单一配置文件
- **生产环境**: `configs/orc_mongodb_service/orc_mongodb_service.yaml`
- **开发环境**: `configs/orc_mongodb_service/development.yaml`

### 3. 自动服务管理
- **自动启动**: 主服务自动启动 MongoDB 写入服务
- **队列监控**: 自动监控 Redis 队列长度，超过阈值时暂停处理
- **状态跟踪**: 实时跟踪所有子服务状态

## 使用方法

### 基本使用

```bash
# 处理ORC数据（使用配置文件中的默认参数）
python3 services/orc_mongodb_service/run.py

# 处理指定日期范围的数据
python3 services/orc_mongodb_service/run.py --start-date 20250629 --end-date 20250630

# 处理指定省份的数据
python3 services/orc_mongodb_service/run.py --province-ids 100 200 210

# 运行监控模式（实时监控服务状态）
python3 services/orc_mongodb_service/run.py --monitor

# 显示当前服务状态
python3 services/orc_mongodb_service/run.py --status
```

### 配置文件

```bash
# 使用生产环境配置
python3 services/orc_mongodb_service/run.py --config configs/orc_mongodb_service/orc_mongodb_service.yaml

# 使用开发环境配置
python3 services/orc_mongodb_service/run.py --config configs/orc_mongodb_service/development.yaml
```

## 配置文件结构

### 主要配置节

```yaml
# 全局配置
project:
  name: "User-DF"
  version: "3.0.0"
  environment: "production"

# ORC处理配置
orc_processor:
  start_date: "20250717"
  end_date: "20250717"
  province_ids: []
  batch_processing:
    batch_size: 1000
    pid_query_batch_size: 30000

# MongoDB写入服务配置
mongodb_writer_service:
  host: "0.0.0.0"
  port: 8002
  batch_processing:
    batch_size: 10

# 监控服务配置
monitoring_service:
  monitoring:
    check_interval: 5

# Redis队列控制配置（核心功能）
redis:
  queue_control:
    check_interval: 10
    pause_threshold: 150    # 队列长度超过此值时暂停ORC处理
    resume_threshold: 20    # 队列长度低于此值时恢复ORC处理

# MongoDB配置
mongodb:
  connection:
    host: "localhost"
    port: 27017
    database: "nrdc"
  collection: "user_pid_records_optimized"

# Milvus配置
milvus:
  connection:
    uri: "http://************:19530"
    token: "nrdc_ilm:Nr@#dc12Ilm"
    database: "nrdc_db"
```

## 核心功能

### 1. Redis队列长度控制
- 自动监控Redis队列长度
- 队列长度超过 `pause_threshold` 时暂停ORC处理
- 队列长度降至 `resume_threshold` 以下时恢复处理
- 避免队列积压导致的内存问题

### 2. 集成监控服务
- 实时监控所有子服务状态
- 显示队列长度、处理统计等信息
- 支持实时监控模式和单次状态检查

### 3. 自动服务管理
- 主服务启动时自动启动MongoDB写入服务
- 统一的服务生命周期管理
- 优雅的服务关闭处理

## 服务架构

```
ORC MongoDB主服务 (main.py)
├── MongoDB写入服务 (自动启动)
│   ├── 端口: 8002
│   ├── 队列处理: Redis队列消费
│   └── 批量写入: MongoDB批量操作
├── 监控服务 (集成)
│   ├── 服务状态监控
│   ├── 队列状态监控
│   └── 实时统计显示
└── ORC处理器 (集成)
    ├── ORC文件扫描
    ├── 批量数据处理
    ├── Milvus PID验证
    └── Redis队列发送
```

## 迁移指南

### 从旧版本迁移

1. **配置文件迁移**:
   - 将分散的子服务配置合并到统一配置文件
   - 更新配置文件路径引用

2. **启动方式变更**:
   ```bash
   # 旧版本
   python3 services/orc_mongodb_service/start_services.py
   
   # 新版本
   python3 services/orc_mongodb_service/run.py
   ```

3. **监控方式变更**:
   ```bash
   # 旧版本 - 需要单独启动监控服务
   python3 services/orc_mongodb_service/monitoring_service/monitor.py
   
   # 新版本 - 集成在主服务中
   python3 services/orc_mongodb_service/run.py --monitor
   ```

## 故障排除

### 常见问题

1. **服务启动失败**:
   - 检查配置文件路径是否正确
   - 确认Redis和MongoDB服务是否运行
   - 查看日志文件获取详细错误信息

2. **队列处理停滞**:
   - 检查Redis队列长度是否超过暂停阈值
   - 确认MongoDB写入服务是否正常运行
   - 调整队列控制参数

3. **Milvus连接问题**:
   - 确认Milvus服务地址和认证信息
   - 检查网络连接
   - 可以临时禁用Milvus过滤进行测试

### 日志文件

- 主服务日志: `logs/orc_mongodb_service/orc_mongodb_service.log`
- 配置文件中可调整日志级别和输出格式

## 性能优化

### 生产环境建议

1. **批处理大小**:
   - `batch_size`: 1000 (用户批次大小)
   - `pid_query_batch_size`: 30000 (PID查询批次)

2. **队列控制**:
   - `pause_threshold`: 150
   - `resume_threshold`: 20

3. **并发配置**:
   - `max_concurrent_files`: 10
   - `max_concurrent_batches`: 20

### 开发环境建议

- 使用较小的批次大小以便调试
- 降低队列阈值以便观察队列控制效果
- 可以禁用Milvus过滤以加快测试速度
